import { Injectable } from '@angular/core';
import * as L from 'leaflet';
import { BehaviorSubject, Observable } from 'rxjs';
import { OpticalLine } from '../../../core/models/optical-line.model';
import { ConnectionPoint } from '../../../core/models/connection-point.model';

@Injectable({
  providedIn: 'root'
})
export class MapService {
  private map?: L.Map;
  private linesLayer = L.layerGroup();
  private pointsLayer = L.layerGroup();

  private mapReadySubject = new BehaviorSubject<boolean>(false);
  public mapReady$ = this.mapReadySubject.asObservable();

  constructor() { }

  /**
   * Initialize the map
   * @param elementId ID of the HTML element to contain the map
   */
  initMap(elementId: string): void {
    // Create the map instance
    this.map = L.map(elementId, {
      center: [48.7, 19.7], // Center of Slovakia as default
      zoom: 8,
      layers: [
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          maxZoom: 19,
          attribution: '© OpenStreetMap contributors'
        })
      ]
    });

    // Add the layers to the map
    this.linesLayer.addTo(this.map);
    this.pointsLayer.addTo(this.map);

    // Notify subscribers that the map is ready
    this.mapReadySubject.next(true);
  }

  /**
   * Display optical lines on the map
   * @param lines Array of optical lines to display
   */
  displayLines(lines: OpticalLine[]): void {
    if (!this.map) return;

    // Clear existing lines
    this.linesLayer.clearLayers();

    // Add each line to the map
    lines.forEach(line => {
      // Transform backend data format to frontend format
      const geometry = this.transformLineGeometry(line);

      if (geometry) {
        try {
          const lineLayer = L.geoJSON(geometry, {
            style: () => ({
              color: this.getProviderColor(line.providerId),
              weight: 3,
              opacity: 0.7
            }),
            onEachFeature: (feature, layer) => {
              layer.bindPopup(this.createLinePopup(line));
            }
          });

          this.linesLayer.addLayer(lineLayer);
        } catch (error) {
          console.error('Error adding line to map:', error, 'Line data:', line);
        }
      } else {
        console.warn('Line missing or invalid geometry:', line);
      }
    });
  }

  /**
   * Display connection points on the map
   * @param points Array of connection points to display
   */
  displayPoints(points: ConnectionPoint[]): void {
    if (!this.map) return;

    // Clear existing points
    this.pointsLayer.clearLayers();

    // Add each point to the map
    points.forEach(point => {
      // Transform backend data format to frontend format
      const coordinates = this.transformPointLocation(point);

      if (coordinates) {
        try {
          const marker = L.marker(coordinates, {
            icon: this.getPointIcon(point.type)
          });

          marker.bindPopup(this.createPointPopup(point));
          this.pointsLayer.addLayer(marker);
        } catch (error) {
          console.error('Error adding point to map:', error, 'Point data:', point);
        }
      } else {
        console.warn('Point missing or invalid location:', point);
      }
    });
  }

  /**
   * Fit the map view to show all lines and points
   */
  fitBounds(): void {
    if (!this.map) return;

    // Check if there are any layers to fit bounds to
    const hasLayers = this.linesLayer.getLayers().length > 0 || this.pointsLayer.getLayers().length > 0;

    if (!hasLayers) {
      // If no layers, just return without trying to fit bounds
      console.log('No layers to fit bounds to');
      return;
    }

    try {
      const bounds = L.featureGroup([this.linesLayer, this.pointsLayer]).getBounds();
      if (bounds.isValid()) {
        this.map.fitBounds(bounds, { padding: [50, 50] });
      }
    } catch (error) {
      console.error('Error fitting bounds:', error);
    }
  }

  /**
   * Get a color based on provider ID
   * @param providerId Provider ID
   * @returns Color string
   */
  private getProviderColor(providerId: string): string {
    // Simple hash function to generate a color from provider ID
    const hash = Array.from(providerId).reduce((acc, char) => {
      return char.charCodeAt(0) + ((acc << 5) - acc);
    }, 0);

    const color = `hsl(${Math.abs(hash) % 360}, 70%, 50%)`;
    return color;
  }

  /**
   * Get an icon for a connection point based on its type
   * @param type Connection point type
   * @returns Leaflet icon
   */
  private getPointIcon(type: string): L.Icon {
    // Define SVG icons as data URLs
    const junctionSvg = 'data:image/svg+xml;base64,' + btoa('<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="10" fill="#ff7800" stroke="#000000" stroke-width="1"/><circle cx="12" cy="12" r="4" fill="#ffffff" stroke="#000000" stroke-width="1"/></svg>');
    const endpointSvg = 'data:image/svg+xml;base64,' + btoa('<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="10" fill="#32CD32" stroke="#000000" stroke-width="1"/><rect x="7" y="7" width="10" height="10" fill="#ffffff" stroke="#000000" stroke-width="1"/></svg>');
    const distributionSvg = 'data:image/svg+xml;base64,' + btoa('<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="10" fill="#9370DB" stroke="#000000" stroke-width="1"/><polygon points="12,6 18,12 12,18 6,12" fill="#ffffff" stroke="#000000" stroke-width="1"/></svg>');

    // Select the appropriate icon based on type
    const iconUrl = type === 'junction'
      ? junctionSvg
      : type === 'endpoint'
        ? endpointSvg
        : distributionSvg;

    return L.icon({
      iconUrl,
      iconSize: [24, 24],
      iconAnchor: [12, 12],
      popupAnchor: [0, -12]
    });
  }

  /**
   * Create a popup content for an optical line
   * @param line Optical line data
   * @returns HTML content for the popup
   */
  private createLinePopup(line: OpticalLine): string {
    return `
      <div class="popup-content">
        <h3>${line.name}</h3>
        <p><strong>Provider:</strong> ${line.providerName}</p>
        <p><strong>Capacity:</strong> ${line.usedCapacity}/${line.capacity}</p>
        <p><strong>Length:</strong> ${line.length} km</p>
        <p><strong>Status:</strong> ${line.status}</p>
        <a href="/lines/${line.id}" target="_blank">View Details</a>
      </div>
    `;
  }

  /**
   * Create a popup content for a connection point
   * @param point Connection point data
   * @returns HTML content for the popup
   */
  private createPointPopup(point: ConnectionPoint): string {
    return `
      <div class="popup-content">
        <h3>${point.name}</h3>
        <p><strong>Provider:</strong> ${point.providerName}</p>
        <p><strong>Type:</strong> ${point.type}</p>
        <p><strong>Address:</strong> ${point.address}</p>
        <p><strong>Status:</strong> ${point.status}</p>
        <a href="/points/${point.id}" target="_blank">View Details</a>
      </div>
    `;
  }

  /**
   * Transform backend line data to GeoJSON LineString format
   * @param line Optical line data from backend
   * @returns GeoJSON LineString or null if invalid
   */
  private transformLineGeometry(line: OpticalLine): GeoJSON.LineString | null {
    // Check if line already has geometry property (frontend format)
    if (line.geometry) {
      return line.geometry as GeoJSON.LineString;
    }

    // Check if line has geometryCoordinates property (backend format)
    const geometryCoordinates = (line as any).geometryCoordinates;
    if (geometryCoordinates) {
      try {
        const coordinates = typeof geometryCoordinates === 'string'
          ? JSON.parse(geometryCoordinates)
          : geometryCoordinates;

        return {
          type: 'LineString',
          coordinates: coordinates
        };
      } catch (error) {
        console.error('Error parsing line geometry coordinates:', error, geometryCoordinates);
        return null;
      }
    }

    return null;
  }

  /**
   * Transform backend point data to Leaflet coordinates format
   * @param point Connection point data from backend
   * @returns [lat, lng] coordinates or null if invalid
   */
  private transformPointLocation(point: ConnectionPoint): [number, number] | null {
    // Check if point already has location property (frontend format)
    if (point.location && (point.location as any).coordinates) {
      const coords = (point.location as any).coordinates;
      return [coords[1], coords[0]]; // GeoJSON is [lng, lat], Leaflet expects [lat, lng]
    }

    // Check if point has latitude/longitude properties (backend format)
    const lat = (point as any).latitude;
    const lng = (point as any).longitude;
    if (typeof lat === 'number' && typeof lng === 'number') {
      return [lat, lng];
    }

    return null;
  }
}
