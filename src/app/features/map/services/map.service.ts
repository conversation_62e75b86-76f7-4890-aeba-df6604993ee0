import { Injectable } from '@angular/core';
import * as L from 'leaflet';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { OpticalLine } from '../../../core/models/optical-line.model';
import { ConnectionPoint } from '../../../core/models/connection-point.model';

export type MapMode = 'view' | 'add-point' | 'add-line';

export interface MapClickEvent {
  latitude: number;
  longitude: number;
  mode: MapMode;
}

@Injectable({
  providedIn: 'root'
})
export class MapService {
  private map?: L.Map;
  private linesLayer = L.layerGroup();
  private pointsLayer = L.layerGroup();
  private tempLayer = L.layerGroup();

  private mapReadySubject = new BehaviorSubject<boolean>(false);
  public mapReady$ = this.mapReadySubject.asObservable();

  private mapModeSubject = new BehaviorSubject<MapMode>('view');
  public mapMode$ = this.mapModeSubject.asObservable();

  private mapClickSubject = new Subject<MapClickEvent>();
  public mapClick$ = this.mapClickSubject.asObservable();

  private lineStartPoint: { latitude: number; longitude: number } | null = null;
  private tempMarker?: L.Marker;

  constructor() { }

  /**
   * Initialize the map
   * @param elementId ID of the HTML element to contain the map
   */
  initMap(elementId: string): void {
    // Create the map instance
    this.map = L.map(elementId, {
      center: [48.7, 19.7], // Center of Slovakia as default
      zoom: 8,
      layers: [
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          maxZoom: 19,
          attribution: '© OpenStreetMap contributors'
        })
      ]
    });

    // Add the layers to the map
    this.linesLayer.addTo(this.map);
    this.pointsLayer.addTo(this.map);
    this.tempLayer.addTo(this.map);

    // Add click event listener
    this.map.on('click', (e: L.LeafletMouseEvent) => {
      this.handleMapClick(e);
    });

    // Notify subscribers that the map is ready
    this.mapReadySubject.next(true);
  }

  /**
   * Display optical lines on the map
   * @param lines Array of optical lines to display
   */
  displayLines(lines: OpticalLine[]): void {
    if (!this.map) return;

    // Clear existing lines
    this.linesLayer.clearLayers();

    // Add each line to the map
    lines.forEach(line => {
      // Transform backend data format to frontend format
      const geometry = this.transformLineGeometry(line);

      if (geometry) {
        try {
          const lineLayer = L.geoJSON(geometry, {
            style: () => ({
              color: this.getProviderColor(line.providerId),
              weight: 3,
              opacity: 0.7
            }),
            onEachFeature: (feature, layer) => {
              layer.bindPopup(this.createLinePopup(line));
            }
          });

          this.linesLayer.addLayer(lineLayer);
        } catch (error) {
          console.error('Error adding line to map:', error, 'Line data:', line);
        }
      } else {
        console.warn('Line missing or invalid geometry:', line);
      }
    });
  }

  /**
   * Display connection points on the map
   * @param points Array of connection points to display
   */
  displayPoints(points: ConnectionPoint[]): void {
    if (!this.map) return;

    // Clear existing points
    this.pointsLayer.clearLayers();

    // Add each point to the map
    points.forEach(point => {
      // Transform backend data format to frontend format
      const coordinates = this.transformPointLocation(point);

      if (coordinates) {
        try {
          const marker = L.marker(coordinates, {
            icon: this.getPointIcon(point.type)
          });

          marker.bindPopup(this.createPointPopup(point));
          this.pointsLayer.addLayer(marker);
        } catch (error) {
          console.error('Error adding point to map:', error, 'Point data:', point);
        }
      } else {
        console.warn('Point missing or invalid location:', point);
      }
    });
  }

  /**
   * Fit the map view to show all lines and points
   */
  fitBounds(): void {
    if (!this.map) return;

    // Check if there are any layers to fit bounds to
    const hasLayers = this.linesLayer.getLayers().length > 0 || this.pointsLayer.getLayers().length > 0;

    if (!hasLayers) {
      // If no layers, just return without trying to fit bounds
      console.log('No layers to fit bounds to');
      return;
    }

    try {
      const bounds = L.featureGroup([this.linesLayer, this.pointsLayer]).getBounds();
      if (bounds.isValid()) {
        this.map.fitBounds(bounds, { padding: [50, 50] });
      }
    } catch (error) {
      console.error('Error fitting bounds:', error);
    }
  }

  /**
   * Get a color based on provider ID
   * @param providerId Provider ID
   * @returns Color string
   */
  private getProviderColor(providerId: string): string {
    // Simple hash function to generate a color from provider ID
    const hash = Array.from(providerId).reduce((acc, char) => {
      return char.charCodeAt(0) + ((acc << 5) - acc);
    }, 0);

    const color = `hsl(${Math.abs(hash) % 360}, 70%, 50%)`;
    return color;
  }

  /**
   * Get an icon for a connection point based on its type
   * @param type Connection point type
   * @returns Leaflet icon
   */
  private getPointIcon(type: string): L.Icon {
    // Define SVG icons as data URLs
    const junctionSvg = 'data:image/svg+xml;base64,' + btoa('<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="10" fill="#ff7800" stroke="#000000" stroke-width="1"/><circle cx="12" cy="12" r="4" fill="#ffffff" stroke="#000000" stroke-width="1"/></svg>');
    const endpointSvg = 'data:image/svg+xml;base64,' + btoa('<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="10" fill="#32CD32" stroke="#000000" stroke-width="1"/><rect x="7" y="7" width="10" height="10" fill="#ffffff" stroke="#000000" stroke-width="1"/></svg>');
    const distributionSvg = 'data:image/svg+xml;base64,' + btoa('<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="10" fill="#9370DB" stroke="#000000" stroke-width="1"/><polygon points="12,6 18,12 12,18 6,12" fill="#ffffff" stroke="#000000" stroke-width="1"/></svg>');

    // Select the appropriate icon based on type
    const iconUrl = type === 'junction'
      ? junctionSvg
      : type === 'endpoint'
        ? endpointSvg
        : distributionSvg;

    return L.icon({
      iconUrl,
      iconSize: [24, 24],
      iconAnchor: [12, 12],
      popupAnchor: [0, -12]
    });
  }

  /**
   * Create a popup content for an optical line
   * @param line Optical line data
   * @returns HTML content for the popup
   */
  private createLinePopup(line: OpticalLine): string {
    return `
      <div class="popup-content">
        <h3>${line.name}</h3>
        <p><strong>Provider:</strong> ${line.providerName}</p>
        <p><strong>Capacity:</strong> ${line.usedCapacity}/${line.capacity}</p>
        <p><strong>Length:</strong> ${line.length} km</p>
        <p><strong>Status:</strong> ${line.status}</p>
        <a href="/lines/${line.id}" target="_blank">View Details</a>
      </div>
    `;
  }

  /**
   * Create a popup content for a connection point
   * @param point Connection point data
   * @returns HTML content for the popup
   */
  private createPointPopup(point: ConnectionPoint): string {
    return `
      <div class="popup-content">
        <h3>${point.name}</h3>
        <p><strong>Provider:</strong> ${point.providerName}</p>
        <p><strong>Type:</strong> ${point.type}</p>
        <p><strong>Address:</strong> ${point.address}</p>
        <p><strong>Status:</strong> ${point.status}</p>
        <a href="/points/${point.id}" target="_blank">View Details</a>
      </div>
    `;
  }

  /**
   * Transform backend line data to GeoJSON LineString format
   * @param line Optical line data from backend
   * @returns GeoJSON LineString or null if invalid
   */
  private transformLineGeometry(line: OpticalLine): GeoJSON.LineString | null {
    // Check if line already has geometry property (frontend format)
    if (line.geometry) {
      return line.geometry as GeoJSON.LineString;
    }

    // Check if line has geometryCoordinates property (backend format)
    const geometryCoordinates = (line as any).geometryCoordinates;
    if (geometryCoordinates) {
      try {
        const coordinates = typeof geometryCoordinates === 'string'
          ? JSON.parse(geometryCoordinates)
          : geometryCoordinates;

        return {
          type: 'LineString',
          coordinates: coordinates
        };
      } catch (error) {
        console.error('Error parsing line geometry coordinates:', error, geometryCoordinates);
        return null;
      }
    }

    return null;
  }

  /**
   * Transform backend point data to Leaflet coordinates format
   * @param point Connection point data from backend
   * @returns [lat, lng] coordinates or null if invalid
   */
  private transformPointLocation(point: ConnectionPoint): [number, number] | null {
    // Check if point already has location property (frontend format)
    if (point.location && (point.location as any).coordinates) {
      const coords = (point.location as any).coordinates;
      return [coords[1], coords[0]]; // GeoJSON is [lng, lat], Leaflet expects [lat, lng]
    }

    // Check if point has latitude/longitude properties (backend format)
    const lat = (point as any).latitude;
    const lng = (point as any).longitude;
    if (typeof lat === 'number' && typeof lng === 'number') {
      return [lat, lng];
    }

    return null;
  }

  /**
   * Set the map interaction mode
   * @param mode The new map mode
   */
  setMapMode(mode: MapMode): void {
    this.mapModeSubject.next(mode);
    this.clearTempLayer();
    this.lineStartPoint = null;

    // Update cursor style based on mode
    if (this.map) {
      const mapContainer = this.map.getContainer();
      mapContainer.style.cursor = mode === 'view' ? '' : 'crosshair';
    }
  }

  /**
   * Get the current map mode
   * @returns Current map mode
   */
  getCurrentMode(): MapMode {
    return this.mapModeSubject.value;
  }

  /**
   * Handle map click events based on current mode
   * @param e Leaflet mouse event
   */
  private handleMapClick(e: L.LeafletMouseEvent): void {
    const mode = this.mapModeSubject.value;
    const clickEvent: MapClickEvent = {
      latitude: e.latlng.lat,
      longitude: e.latlng.lng,
      mode: mode
    };

    if (mode === 'add-point') {
      this.handleAddPointClick(clickEvent);
    } else if (mode === 'add-line') {
      this.handleAddLineClick(clickEvent);
    }

    // Always emit the click event for components to handle
    this.mapClickSubject.next(clickEvent);
  }

  /**
   * Handle click for adding a point
   * @param clickEvent Map click event
   */
  private handleAddPointClick(clickEvent: MapClickEvent): void {
    // Clear any existing temp markers
    this.clearTempLayer();

    // Add a temporary marker to show where the point will be created
    const tempIcon = L.icon({
      iconUrl: 'data:image/svg+xml;base64,' + btoa('<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="10" fill="#ff0000" stroke="#ffffff" stroke-width="2" opacity="0.7"/><text x="12" y="16" text-anchor="middle" fill="white" font-size="12">+</text></svg>'),
      iconSize: [24, 24],
      iconAnchor: [12, 12]
    });

    this.tempMarker = L.marker([clickEvent.latitude, clickEvent.longitude], { icon: tempIcon });
    this.tempLayer.addLayer(this.tempMarker);
  }

  /**
   * Handle click for adding a line
   * @param clickEvent Map click event
   */
  private handleAddLineClick(clickEvent: MapClickEvent): void {
    if (!this.lineStartPoint) {
      // First click - set start point
      this.lineStartPoint = {
        latitude: clickEvent.latitude,
        longitude: clickEvent.longitude
      };

      // Add start point marker
      const startIcon = L.icon({
        iconUrl: 'data:image/svg+xml;base64,' + btoa('<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="10" fill="#00ff00" stroke="#ffffff" stroke-width="2"/><text x="12" y="16" text-anchor="middle" fill="white" font-size="10">S</text></svg>'),
        iconSize: [24, 24],
        iconAnchor: [12, 12]
      });

      this.tempMarker = L.marker([clickEvent.latitude, clickEvent.longitude], { icon: startIcon });
      this.tempLayer.addLayer(this.tempMarker);
    } else {
      // Second click - set end point and emit line creation event
      const endIcon = L.icon({
        iconUrl: 'data:image/svg+xml;base64,' + btoa('<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="10" fill="#ff0000" stroke="#ffffff" stroke-width="2"/><text x="12" y="16" text-anchor="middle" fill="white" font-size="10">E</text></svg>'),
        iconSize: [24, 24],
        iconAnchor: [12, 12]
      });

      const endMarker = L.marker([clickEvent.latitude, clickEvent.longitude], { icon: endIcon });
      this.tempLayer.addLayer(endMarker);

      // Add temporary line
      const tempLine = L.polyline([
        [this.lineStartPoint.latitude, this.lineStartPoint.longitude],
        [clickEvent.latitude, clickEvent.longitude]
      ], {
        color: '#ff0000',
        weight: 3,
        opacity: 0.7,
        dashArray: '10, 10'
      });
      this.tempLayer.addLayer(tempLine);

      // Emit line creation event with both points
      this.mapClickSubject.next({
        ...clickEvent,
        mode: 'add-line'
      });
    }
  }

  /**
   * Clear temporary layer (markers, lines, etc.)
   */
  clearTempLayer(): void {
    this.tempLayer.clearLayers();
    this.tempMarker = undefined;
  }

  /**
   * Reset line creation state
   */
  resetLineCreation(): void {
    this.lineStartPoint = null;
    this.clearTempLayer();
  }

  /**
   * Get the current line start point (for line creation)
   * @returns Start point coordinates or null
   */
  getLineStartPoint(): { latitude: number; longitude: number } | null {
    return this.lineStartPoint;
  }

  /**
   * Refresh the map data by re-displaying points and lines
   * @param points Array of connection points
   * @param lines Array of optical lines
   */
  refreshData(points: ConnectionPoint[], lines: OpticalLine[]): void {
    this.displayPoints(points);
    this.displayLines(lines);
  }
}
