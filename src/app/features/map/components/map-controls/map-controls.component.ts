import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatCardModule } from '@angular/material/card';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { FormsModule } from '@angular/forms';
import { LayerInfo } from '../../services/layer.service';
import { MapMode } from '../../services/map.service';

@Component({
  selector: 'app-map-controls',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatCardModule,
    MatSlideToggleModule,
    MatButtonToggleModule,
    FormsModule
  ],
  template: `
    <div class="map-controls">
      <mat-card>
        <mat-card-content>
          <div class="control-group">
            <h3>Map Mode</h3>
            <mat-button-toggle-group [value]="currentMode" (change)="onModeChange($event.value)">
              <mat-button-toggle value="view" matTooltip="View Mode">
                <mat-icon>visibility</mat-icon>
              </mat-button-toggle>
              <mat-button-toggle value="add-point" matTooltip="Add Point Mode">
                <mat-icon>add_location</mat-icon>
              </mat-button-toggle>
              <mat-button-toggle value="add-line" matTooltip="Add Line Mode">
                <mat-icon>timeline</mat-icon>
              </mat-button-toggle>
            </mat-button-toggle-group>
          </div>

          <div class="control-group">
            <button mat-icon-button matTooltip="Zoom In" (click)="zoomIn.emit()">
              <mat-icon>add</mat-icon>
            </button>
            <button mat-icon-button matTooltip="Zoom Out" (click)="zoomOut.emit()">
              <mat-icon>remove</mat-icon>
            </button>
            <button mat-icon-button matTooltip="Reset View" (click)="resetView.emit()">
              <mat-icon>home</mat-icon>
            </button>
          </div>
          
          <div class="control-group">
            <h3>Layers</h3>
            <div *ngFor="let layer of layers" class="layer-control">
              <mat-slide-toggle 
                [checked]="layer.visible"
                (change)="toggleLayer.emit(layer.id)">
                {{ layer.name }}
              </mat-slide-toggle>
            </div>
          </div>
          
          <div class="control-group">
            <h3>Base Map</h3>
            <div class="base-layer-control">
              <select (change)="changeBaseLayer.emit($any($event.target).value)">
                <option value="osm" [selected]="activeBaseLayer === 'osm'">OpenStreetMap</option>
                <option value="light" [selected]="activeBaseLayer === 'light'">Light</option>
                <option value="dark" [selected]="activeBaseLayer === 'dark'">Dark</option>
                <option value="satellite" [selected]="activeBaseLayer === 'satellite'">Satellite</option>
              </select>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: `
    .map-controls {
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 1000;
      width: 200px;
    }
    
    .control-group {
      margin-bottom: 15px;
    }

    .control-group h3 {
      margin: 0 0 8px 0;
      font-size: 14px;
    }

    mat-button-toggle-group {
      display: flex;
      flex-direction: column;
      width: 100%;
    }

    mat-button-toggle {
      margin-bottom: 2px;
    }
    
    .layer-control, .base-layer-control {
      margin-bottom: 5px;
    }
    
    select {
      width: 100%;
      padding: 5px;
    }
  `
})
export class MapControlsComponent {
  @Input() layers: LayerInfo[] = [];
  @Input() activeBaseLayer: string = 'osm';
  @Input() currentMode: MapMode = 'view';

  @Output() zoomIn = new EventEmitter<void>();
  @Output() zoomOut = new EventEmitter<void>();
  @Output() resetView = new EventEmitter<void>();
  @Output() toggleLayer = new EventEmitter<string>();
  @Output() changeBaseLayer = new EventEmitter<string>();
  @Output() modeChange = new EventEmitter<MapMode>();

  onModeChange(mode: MapMode): void {
    this.modeChange.emit(mode);
  }
}
