import { Component, OnInit, After<PERSON>iew<PERSON><PERSON>t, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDialog } from '@angular/material/dialog';
import * as L from 'leaflet';
import { Subscription, forkJoin } from 'rxjs';

import { MapService, GeoJsonService, LayerService, LayerInfo, MapMode, MapClickEvent } from '../../services';
import { MapControlsComponent, MapLegendComponent } from '../../components';
import { OpticalLineService, ConnectionPointService, AuthService } from '../../../../core/services';
import { OpticalLine, ConnectionPoint, User } from '../../../../core/models';
import { Al<PERSON>Component } from '../../../../shared/components/alert/alert.component';
import { PointCreationDialogComponent } from '../../../../shared/components/point-creation-dialog/point-creation-dialog.component';
import { LineCreationDialogComponent } from '../../../../shared/components/line-creation-dialog/line-creation-dialog.component';

@Component({
  selector: 'app-map-view',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MapControlsComponent,
    MapLegendComponent,
    AlertComponent
  ],
  template: `
    <div class="map-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>Optical Network Map</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <app-alert *ngIf="error" [message]="error" type="error"></app-alert>

          <div class="map-wrapper">
            <div id="map" class="map"></div>

            <div *ngIf="loading" class="loading-overlay">
              <mat-spinner diameter="50"></mat-spinner>
            </div>

            <app-map-controls
              [layers]="layers"
              [activeBaseLayer]="activeBaseLayer"
              [currentMode]="currentMode"
              (zoomIn)="zoomIn()"
              (zoomOut)="zoomOut()"
              (resetView)="resetView()"
              (toggleLayer)="toggleLayer($event)"
              (changeBaseLayer)="changeBaseLayer($event)"
              (modeChange)="onModeChange($event)">
            </app-map-controls>

            <app-map-legend></app-map-legend>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: `
    .map-container {
      padding: 20px;
    }

    .map-wrapper {
      position: relative;
      height: 600px;
      border: 1px solid #ddd;
    }

    .map {
      height: 100%;
      width: 100%;
    }

    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.7);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1001;
    }
  `
})
export class MapViewComponent implements OnInit, AfterViewInit, OnDestroy {
  loading = true;
  error = '';
  layers: LayerInfo[] = [];
  activeBaseLayer = 'osm';
  currentMode: MapMode = 'view';
  currentUser: User | null = null;

  private subscriptions: Subscription[] = [];
  private lines: OpticalLine[] = [];
  private points: ConnectionPoint[] = [];

  constructor(
    private mapService: MapService,
    private layerService: LayerService,
    private geoJsonService: GeoJsonService,
    private lineService: OpticalLineService,
    private pointService: ConnectionPointService,
    private authService: AuthService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    // Get current user
    this.currentUser = this.authService.getCurrentUser();

    // Subscribe to layer changes
    this.subscriptions.push(
      this.layerService.layers$.subscribe(layers => {
        this.layers = layers;
      })
    );

    // Subscribe to map mode changes
    this.subscriptions.push(
      this.mapService.mapMode$.subscribe(mode => {
        this.currentMode = mode;
      })
    );

    // Subscribe to map click events
    this.subscriptions.push(
      this.mapService.mapClick$.subscribe(clickEvent => {
        this.handleMapClick(clickEvent);
      })
    );

    // Load data
    this.loadData();
  }

  ngAfterViewInit(): void {
    // Initialize the map
    setTimeout(() => {
      this.mapService.initMap('map');
    });
  }

  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Load optical lines and connection points data
   */
  private loadData(): void {
    this.loading = true;

    forkJoin({
      lines: this.lineService.getLines(),
      points: this.pointService.getPoints()
    }).subscribe({
      next: (data) => {
        this.lines = data.lines;
        this.points = data.points;
        this.displayData();
        this.loading = false;
      },
      error: (err) => {
        this.error = 'Failed to load map data. Please try again later.';
        this.loading = false;
        console.error('Error loading map data:', err);
      }
    });
  }

  /**
   * Display the loaded data on the map
   */
  private displayData(): void {
    // Subscribe to map ready event
    this.subscriptions.push(
      this.mapService.mapReady$.subscribe(ready => {
        if (ready) {
          // Display lines and points
          this.mapService.displayLines(this.lines);
          this.mapService.displayPoints(this.points);

          // Fit the map to show all data
          this.mapService.fitBounds();
        }
      })
    );
  }

  /**
   * Zoom in on the map
   */
  zoomIn(): void {
    const map = (this.mapService as any).map;
    if (map) {
      map.zoomIn();
    }
  }

  /**
   * Zoom out on the map
   */
  zoomOut(): void {
    const map = (this.mapService as any).map;
    if (map) {
      map.zoomOut();
    }
  }

  /**
   * Reset the map view to show all data
   */
  resetView(): void {
    this.mapService.fitBounds();
  }

  /**
   * Toggle a layer's visibility
   * @param layerId Layer ID to toggle
   */
  toggleLayer(layerId: string): void {
    this.layerService.toggleLayerVisibility(layerId);
  }

  /**
   * Change the base layer
   * @param layerId Base layer ID
   */
  changeBaseLayer(layerId: string): void {
    this.activeBaseLayer = layerId;
    this.layerService.setActiveBaseLayer(layerId);
  }

  /**
   * Handle map mode change
   * @param mode New map mode
   */
  onModeChange(mode: MapMode): void {
    this.mapService.setMapMode(mode);
  }

  /**
   * Handle map click events
   * @param clickEvent Map click event
   */
  private handleMapClick(clickEvent: MapClickEvent): void {
    if (!this.currentUser) {
      this.error = 'You must be logged in to add points or lines.';
      return;
    }

    if (clickEvent.mode === 'add-point') {
      this.openPointCreationDialog(clickEvent);
    } else if (clickEvent.mode === 'add-line') {
      this.handleLineCreation(clickEvent);
    }
  }

  /**
   * Open point creation dialog
   * @param clickEvent Map click event
   */
  private openPointCreationDialog(clickEvent: MapClickEvent): void {
    const dialogRef = this.dialog.open(PointCreationDialogComponent, {
      width: '500px',
      data: {
        latitude: clickEvent.latitude,
        longitude: clickEvent.longitude
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Point was created successfully
        this.loadData(); // Reload data to show the new point
        this.mapService.setMapMode('view'); // Switch back to view mode
      }
      this.mapService.clearTempLayer(); // Clear temporary markers
    });
  }

  /**
   * Handle line creation logic
   * @param clickEvent Map click event
   */
  private handleLineCreation(clickEvent: MapClickEvent): void {
    const startPoint = this.mapService.getLineStartPoint();

    if (startPoint) {
      // Second click - open line creation dialog
      const dialogRef = this.dialog.open(LineCreationDialogComponent, {
        width: '600px',
        data: {
          startPoint: startPoint,
          endPoint: {
            latitude: clickEvent.latitude,
            longitude: clickEvent.longitude
          }
        }
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          // Line was created successfully
          this.loadData(); // Reload data to show the new line
        }
        this.mapService.resetLineCreation(); // Reset line creation state
        this.mapService.setMapMode('view'); // Switch back to view mode
      });
    }
    // First click is handled automatically by the map service
  }
}
