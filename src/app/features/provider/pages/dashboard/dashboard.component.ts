import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatGridListModule } from '@angular/material/grid-list';
import { RouterLink } from '@angular/router';
import { AuthService } from '../../../../core/services/auth.service';
import { User } from '../../../../core/models';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatGridListModule,
    RouterLink
  ],
  template: `
    <div class="dashboard-container">
      <!-- Welcome Section -->
      <mat-card class="welcome-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>dashboard</mat-icon>
            Provider Dashboard
          </mat-card-title>
          <mat-card-subtitle *ngIf="currentUser">
            Welcome back, {{ currentUser.providerName || currentUser.username }}!
          </mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <p>Manage your optical lines, connection points, and monitor capacity utilization.</p>
        </mat-card-content>
      </mat-card>

      <!-- Quick Actions Grid -->
      <div class="quick-actions-grid">
        <mat-card class="action-card" routerLink="/provider/lines">
          <mat-card-content>
            <div class="action-content">
              <mat-icon class="action-icon">timeline</mat-icon>
              <h3>My Lines</h3>
              <p>Manage your optical lines and infrastructure</p>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="action-card" routerLink="/provider/points">
          <mat-card-content>
            <div class="action-content">
              <mat-icon class="action-icon">place</mat-icon>
              <h3>Connection Points</h3>
              <p>Manage your connection points and locations</p>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="action-card" routerLink="/provider/capacity">
          <mat-card-content>
            <div class="action-content">
              <mat-icon class="action-icon">data_usage</mat-icon>
              <h3>Capacity Management</h3>
              <p>Monitor and manage line capacity utilization</p>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="action-card" routerLink="/auth/profile">
          <mat-card-content>
            <div class="action-content">
              <mat-icon class="action-icon">person</mat-icon>
              <h3>Profile Settings</h3>
              <p>Update your provider information and settings</p>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- Statistics Overview -->
      <mat-card class="stats-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>analytics</mat-icon>
            Overview Statistics
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-value">--</div>
              <div class="stat-label">Total Lines</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">--</div>
              <div class="stat-label">Connection Points</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">--</div>
              <div class="stat-label">Avg. Capacity</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">--</div>
              <div class="stat-label">Active Alerts</div>
            </div>
          </div>
          <div class="stats-note">
            <mat-icon>info</mat-icon>
            <span>Statistics will be available once backend APIs are implemented</span>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: `
    .dashboard-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .welcome-card {
      margin-bottom: 24px;
    }

    .welcome-card mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .quick-actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 16px;
      margin-bottom: 24px;
    }

    .action-card {
      cursor: pointer;
      transition: all 0.3s ease;
      height: 140px;
    }

    .action-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .action-content {
      text-align: center;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }

    .action-icon {
      font-size: 32px;
      width: 32px;
      height: 32px;
      margin-bottom: 8px;
      color: #1976d2;
    }

    .action-content h3 {
      margin: 8px 0 4px 0;
      font-size: 16px;
      font-weight: 500;
    }

    .action-content p {
      margin: 0;
      font-size: 12px;
      color: #666;
      text-align: center;
    }

    .stats-card mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 16px;
      margin-bottom: 16px;
    }

    .stat-item {
      text-align: center;
      padding: 16px;
      background: #f5f5f5;
      border-radius: 8px;
    }

    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #1976d2;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 12px;
      color: #666;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .stats-note {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px;
      background: #e3f2fd;
      border-radius: 4px;
      font-size: 14px;
      color: #1565c0;
    }

    .stats-note mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    @media (max-width: 768px) {
      .dashboard-container {
        padding: 16px;
      }

      .quick-actions-grid {
        grid-template-columns: 1fr;
      }

      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  `
})
export class DashboardComponent implements OnInit {
  currentUser: User | null = null;

  constructor(private authService: AuthService) {}

  ngOnInit(): void {
    this.currentUser = this.authService.getCurrentUser();
  }
}
