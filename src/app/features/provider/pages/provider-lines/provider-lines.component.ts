import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';

@Component({
  selector: 'app-provider-lines',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule
  ],
  template: `
    <div class="provider-lines-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>timeline</mat-icon>
            My Optical Lines
          </mat-card-title>
          <mat-card-subtitle>
            Manage your optical line infrastructure
          </mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <mat-tab-group>
            <mat-tab label="All Lines">
              <div class="tab-content">
                <div class="action-bar">
                  <button mat-raised-button color="primary">
                    <mat-icon>add</mat-icon>
                    Add New Line
                  </button>
                  <button mat-button>
                    <mat-icon>import_export</mat-icon>
                    Import/Export
                  </button>
                </div>

                <div class="placeholder-content">
                  <mat-icon class="placeholder-icon">timeline</mat-icon>
                  <h3>Lines Management Coming Soon</h3>
                  <p>This section will allow you to:</p>
                  <ul>
                    <li>View all your optical lines</li>
                    <li>Add new line segments</li>
                    <li>Edit existing line properties</li>
                    <li>Monitor line status and capacity</li>
                    <li>Export line data</li>
                  </ul>
                  <p class="note">
                    <mat-icon>info</mat-icon>
                    Backend API endpoints need to be implemented first
                  </p>
                </div>
              </div>
            </mat-tab>

            <mat-tab label="Active Lines">
              <div class="tab-content">
                <div class="placeholder-content">
                  <mat-icon class="placeholder-icon">check_circle</mat-icon>
                  <h3>Active Lines Overview</h3>
                  <p>View and manage currently active optical lines</p>
                </div>
              </div>
            </mat-tab>

            <mat-tab label="Maintenance">
              <div class="tab-content">
                <div class="placeholder-content">
                  <mat-icon class="placeholder-icon">build</mat-icon>
                  <h3>Maintenance Schedule</h3>
                  <p>Track maintenance activities and schedules for your lines</p>
                </div>
              </div>
            </mat-tab>
          </mat-tab-group>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: `
    .provider-lines-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .tab-content {
      padding: 20px 0;
    }

    .action-bar {
      display: flex;
      gap: 12px;
      margin-bottom: 24px;
      flex-wrap: wrap;
    }

    .action-bar button {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .placeholder-content {
      text-align: center;
      padding: 40px 20px;
      background: #f9f9f9;
      border-radius: 8px;
      border: 2px dashed #ddd;
    }

    .placeholder-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: #1976d2;
      margin-bottom: 16px;
    }

    .placeholder-content h3 {
      margin: 0 0 16px 0;
      color: #333;
    }

    .placeholder-content p {
      margin: 0 0 16px 0;
      color: #666;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }

    .placeholder-content ul {
      text-align: left;
      max-width: 400px;
      margin: 16px auto;
      color: #666;
    }

    .placeholder-content li {
      margin-bottom: 8px;
    }

    .note {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 12px;
      background: #e3f2fd;
      border-radius: 4px;
      font-size: 14px;
      color: #1565c0;
      margin-top: 20px;
    }

    .note mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    @media (max-width: 768px) {
      .provider-lines-container {
        padding: 16px;
      }

      .action-bar {
        flex-direction: column;
      }

      .action-bar button {
        width: 100%;
        justify-content: center;
      }
    }
  `
})
export class ProviderLinesComponent {}
