import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatChipsModule } from '@angular/material/chips';

@Component({
  selector: 'app-capacity-management',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatProgressBarModule,
    MatChipsModule
  ],
  template: `
    <div class="capacity-management-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>data_usage</mat-icon>
            Capacity Management
          </mat-card-title>
          <mat-card-subtitle>
            Monitor and manage line capacity utilization
          </mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <mat-tab-group>
            <mat-tab label="Overview">
              <div class="tab-content">
                <div class="capacity-overview">
                  <div class="overview-cards">
                    <div class="overview-card">
                      <mat-icon class="overview-icon">timeline</mat-icon>
                      <div class="overview-content">
                        <div class="overview-value">--</div>
                        <div class="overview-label">Total Lines</div>
                      </div>
                    </div>

                    <div class="overview-card">
                      <mat-icon class="overview-icon">data_usage</mat-icon>
                      <div class="overview-content">
                        <div class="overview-value">--%</div>
                        <div class="overview-label">Avg. Utilization</div>
                      </div>
                    </div>

                    <div class="overview-card">
                      <mat-icon class="overview-icon">warning</mat-icon>
                      <div class="overview-content">
                        <div class="overview-value">--</div>
                        <div class="overview-label">Capacity Alerts</div>
                      </div>
                    </div>
                  </div>

                  <div class="placeholder-content">
                    <mat-icon class="placeholder-icon">data_usage</mat-icon>
                    <h3>Capacity Overview Coming Soon</h3>
                    <p>This section will provide:</p>
                    <ul>
                      <li>Real-time capacity utilization metrics</li>
                      <li>Capacity trend analysis</li>
                      <li>Threshold alerts and notifications</li>
                      <li>Performance optimization recommendations</li>
                    </ul>
                  </div>
                </div>
              </div>
            </mat-tab>

            <mat-tab label="Line Details">
              <div class="tab-content">
                <div class="action-bar">
                  <button mat-button>
                    <mat-icon>refresh</mat-icon>
                    Refresh Data
                  </button>
                  <button mat-button>
                    <mat-icon>settings</mat-icon>
                    Configure Thresholds
                  </button>
                </div>

                <div class="placeholder-content">
                  <mat-icon class="placeholder-icon">timeline</mat-icon>
                  <h3>Line Capacity Details</h3>
                  <p>Detailed capacity information for each optical line</p>
                </div>
              </div>
            </mat-tab>

            <mat-tab label="Alerts">
              <div class="tab-content">
                <div class="alerts-section">
                  <div class="alert-item sample-alert">
                    <mat-icon class="alert-icon">info</mat-icon>
                    <div class="alert-content">
                      <div class="alert-title">Sample Alert</div>
                      <div class="alert-message">This is how capacity alerts will appear once the backend is implemented</div>
                    </div>
                    <mat-chip class="alert-chip">Info</mat-chip>
                  </div>
                </div>

                <div class="placeholder-content">
                  <mat-icon class="placeholder-icon">notifications</mat-icon>
                  <h3>Capacity Alerts & Notifications</h3>
                  <p>Monitor capacity thresholds and receive alerts when limits are approached</p>
                </div>
              </div>
            </mat-tab>
          </mat-tab-group>

          <div class="implementation-note">
            <mat-icon>info</mat-icon>
            <span>Backend API endpoints for capacity management need to be implemented</span>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: `
    .capacity-management-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .tab-content {
      padding: 20px 0;
    }

    .capacity-overview {
      margin-bottom: 24px;
    }

    .overview-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
      margin-bottom: 24px;
    }

    .overview-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background: #f5f5f5;
      border-radius: 8px;
      gap: 16px;
    }

    .overview-icon {
      font-size: 32px;
      width: 32px;
      height: 32px;
      color: #1976d2;
    }

    .overview-content {
      flex: 1;
    }

    .overview-value {
      font-size: 24px;
      font-weight: bold;
      color: #333;
      margin-bottom: 4px;
    }

    .overview-label {
      font-size: 12px;
      color: #666;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .action-bar {
      display: flex;
      gap: 12px;
      margin-bottom: 24px;
      flex-wrap: wrap;
    }

    .action-bar button {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .alerts-section {
      margin-bottom: 24px;
    }

    .alert-item {
      display: flex;
      align-items: center;
      padding: 16px;
      background: #e3f2fd;
      border-radius: 8px;
      gap: 12px;
      margin-bottom: 12px;
    }

    .sample-alert {
      border-left: 4px solid #2196f3;
    }

    .alert-icon {
      color: #2196f3;
    }

    .alert-content {
      flex: 1;
    }

    .alert-title {
      font-weight: 500;
      margin-bottom: 4px;
    }

    .alert-message {
      font-size: 14px;
      color: #666;
    }

    .alert-chip {
      background: #2196f3;
      color: white;
    }

    .placeholder-content {
      text-align: center;
      padding: 40px 20px;
      background: #f9f9f9;
      border-radius: 8px;
      border: 2px dashed #ddd;
    }

    .placeholder-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: #1976d2;
      margin-bottom: 16px;
    }

    .placeholder-content h3 {
      margin: 0 0 16px 0;
      color: #333;
    }

    .placeholder-content p {
      margin: 0 0 16px 0;
      color: #666;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }

    .placeholder-content ul {
      text-align: left;
      max-width: 400px;
      margin: 16px auto;
      color: #666;
    }

    .placeholder-content li {
      margin-bottom: 8px;
    }

    .implementation-note {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px;
      background: #fff3e0;
      border-radius: 4px;
      font-size: 14px;
      color: #f57c00;
      margin-top: 20px;
    }

    .implementation-note mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    @media (max-width: 768px) {
      .capacity-management-container {
        padding: 16px;
      }

      .overview-cards {
        grid-template-columns: 1fr;
      }

      .action-bar {
        flex-direction: column;
      }

      .action-bar button {
        width: 100%;
        justify-content: center;
      }
    }
  `
})
export class CapacityManagementComponent {}
