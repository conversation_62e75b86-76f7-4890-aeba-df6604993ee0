import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';

@Component({
  selector: 'app-provider-points',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule
  ],
  template: `
    <div class="provider-points-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>place</mat-icon>
            My Connection Points
          </mat-card-title>
          <mat-card-subtitle>
            Manage your connection point infrastructure
          </mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <mat-tab-group>
            <mat-tab label="All Points">
              <div class="tab-content">
                <div class="action-bar">
                  <button mat-raised-button color="primary">
                    <mat-icon>add</mat-icon>
                    Add New Point
                  </button>
                  <button mat-button>
                    <mat-icon>map</mat-icon>
                    View on Map
                  </button>
                  <button mat-button>
                    <mat-icon>import_export</mat-icon>
                    Import/Export
                  </button>
                </div>

                <div class="placeholder-content">
                  <mat-icon class="placeholder-icon">place</mat-icon>
                  <h3>Connection Points Management Coming Soon</h3>
                  <p>This section will allow you to:</p>
                  <ul>
                    <li>View all your connection points</li>
                    <li>Add new connection points</li>
                    <li>Edit point properties and locations</li>
                    <li>Monitor point status and capacity</li>
                    <li>View points on interactive map</li>
                    <li>Export point data</li>
                  </ul>
                  <p class="note">
                    <mat-icon>info</mat-icon>
                    Backend API endpoints need to be implemented first
                  </p>
                </div>
              </div>
            </mat-tab>

            <mat-tab label="Active Points">
              <div class="tab-content">
                <div class="placeholder-content">
                  <mat-icon class="placeholder-icon">check_circle</mat-icon>
                  <h3>Active Connection Points</h3>
                  <p>View and manage currently active connection points</p>
                </div>
              </div>
            </mat-tab>

            <mat-tab label="Capacity Status">
              <div class="tab-content">
                <div class="placeholder-content">
                  <mat-icon class="placeholder-icon">data_usage</mat-icon>
                  <h3>Point Capacity Overview</h3>
                  <p>Monitor capacity utilization across your connection points</p>
                </div>
              </div>
            </mat-tab>
          </mat-tab-group>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: `
    .provider-points-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .tab-content {
      padding: 20px 0;
    }

    .action-bar {
      display: flex;
      gap: 12px;
      margin-bottom: 24px;
      flex-wrap: wrap;
    }

    .action-bar button {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .placeholder-content {
      text-align: center;
      padding: 40px 20px;
      background: #f9f9f9;
      border-radius: 8px;
      border: 2px dashed #ddd;
    }

    .placeholder-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: #1976d2;
      margin-bottom: 16px;
    }

    .placeholder-content h3 {
      margin: 0 0 16px 0;
      color: #333;
    }

    .placeholder-content p {
      margin: 0 0 16px 0;
      color: #666;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }

    .placeholder-content ul {
      text-align: left;
      max-width: 400px;
      margin: 16px auto;
      color: #666;
    }

    .placeholder-content li {
      margin-bottom: 8px;
    }

    .note {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 12px;
      background: #e3f2fd;
      border-radius: 4px;
      font-size: 14px;
      color: #1565c0;
      margin-top: 20px;
    }

    .note mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    @media (max-width: 768px) {
      .provider-points-container {
        padding: 16px;
      }

      .action-bar {
        flex-direction: column;
      }

      .action-bar button {
        width: 100%;
        justify-content: center;
      }
    }
  `
})
export class ProviderPointsComponent {}
