import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { MatTabsModule } from '@angular/material/tabs';

import { AuthService } from '../../../../core/services/auth.service';
import { AlertComponent } from '../../../../shared/components/alert/alert.component';
import { User } from '../../../../core/models';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatTabsModule,
    AlertComponent
  ],
  template: `
    <div class="profile-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>User Profile</mat-card-title>
          <mat-card-subtitle *ngIf="user">{{ user.username }}</mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <app-alert *ngIf="error" [message]="error" type="error"></app-alert>
          <app-alert *ngIf="success" [message]="success" type="success"></app-alert>

          <div *ngIf="loading" class="loading-container">
            <mat-spinner diameter="40"></mat-spinner>
          </div>

          <div *ngIf="user && !loading">
            <mat-tab-group>
              <mat-tab label="Profile Information">
                <div class="tab-content">
                  <div class="info-grid">
                    <div class="info-item">
                      <div class="info-label">Username</div>
                      <div class="info-value">{{ user.username }}</div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">Email</div>
                      <div class="info-value">{{ user.email }}</div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">Role</div>
                      <div class="info-value">{{ user.role | titlecase }}</div>
                    </div>

                    <div class="info-item" *ngIf="user.providerName">
                      <div class="info-label">Provider Name</div>
                      <div class="info-value">{{ user.providerName }}</div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">Account Created</div>
                      <div class="info-value">{{ user.createdAt | date }}</div>
                    </div>

                    <div class="info-item">
                      <div class="info-label">Last Login</div>
                      <div class="info-value">{{ user.lastLogin | date:'medium' }}</div>
                    </div>
                  </div>
                </div>
              </mat-tab>

              <mat-tab label="Edit Profile">
                <div class="tab-content">
                  <form [formGroup]="profileForm" (ngSubmit)="onSubmit()">
                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>Username</mat-label>
                      <input matInput formControlName="username" placeholder="Enter your username">
                      <mat-error *ngIf="profileForm.get('username')?.hasError('required')">
                        Username is required
                      </mat-error>
                      <mat-error *ngIf="profileForm.get('username')?.hasError('minlength')">
                        Username must be at least 3 characters
                      </mat-error>
                    </mat-form-field>

                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>Email</mat-label>
                      <input matInput formControlName="email" type="email" placeholder="Enter your email" readonly>
                      <mat-hint>Email cannot be changed</mat-hint>
                    </mat-form-field>

                    <mat-form-field appearance="outline" class="full-width" *ngIf="user.role === 'provider'">
                      <mat-label>Provider Name</mat-label>
                      <input matInput formControlName="providerName" placeholder="Enter your provider name">
                      <mat-error *ngIf="profileForm.get('providerName')?.hasError('required')">
                        Provider name is required
                      </mat-error>
                    </mat-form-field>

                    <div class="form-actions">
                      <button mat-raised-button color="primary" type="submit" [disabled]="profileForm.invalid || updateLoading">
                        <mat-spinner diameter="20" *ngIf="updateLoading"></mat-spinner>
                        <span *ngIf="!updateLoading">Update Profile</span>
                      </button>
                    </div>
                  </form>
                </div>
              </mat-tab>
            </mat-tab-group>

            <mat-divider class="divider"></mat-divider>

            <div class="logout-section">
              <button mat-raised-button color="warn" (click)="onLogout()">
                <mat-icon>exit_to_app</mat-icon> Logout
              </button>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: `
    .profile-container {
      padding: 20px;
    }

    .loading-container {
      display: flex;
      justify-content: center;
      padding: 40px;
    }

    .tab-content {
      padding: 20px 0;
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
    }

    .info-item {
      display: flex;
      flex-direction: column;
      margin-bottom: 15px;
    }

    .info-label {
      font-weight: 500;
      color: rgba(0, 0, 0, 0.54);
      margin-bottom: 5px;
    }

    .info-value {
      font-size: 16px;
    }

    .full-width {
      width: 100%;
      margin-bottom: 15px;
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
    }

    .form-actions button {
      min-width: 120px;
      min-height: 36px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .divider {
      margin: 20px 0;
    }

    .logout-section {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  `
})
export class ProfileComponent implements OnInit {
  user: User | null = null;
  profileForm: FormGroup;
  loading = true;
  updateLoading = false;
  error = '';
  success = '';

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router
  ) {
    this.profileForm = this.fb.group({
      username: ['', [Validators.required, Validators.minLength(3)]],
      email: [{ value: '', disabled: true }],
      providerName: ['']
    });
  }

  ngOnInit(): void {
    this.loadUserProfile();
  }

  loadUserProfile(): void {
    this.loading = true;

    // Get the current user from the auth service
    this.user = this.authService.getCurrentUser();

    console.log('Profile component - Current user:', this.user);
    console.log('Profile component - Is authenticated:', this.authService.isAuthenticated$);

    if (this.user) {
      console.log('User found, loading profile data');
      // Initialize the form with user data
      this.profileForm.patchValue({
        username: this.user.username,
        email: this.user.email,
        providerName: this.user.providerName || ''
      });

      // Add validation for provider name if the user is a provider
      if (this.user.role === 'provider') {
        this.profileForm.get('providerName')?.setValidators([Validators.required]);
        this.profileForm.get('providerName')?.updateValueAndValidity();
      }

      this.loading = false;
    } else {
      console.log('No user found, redirecting to login');
      // If no user is found, redirect to login
      this.router.navigate(['/auth/login']);
    }
  }

  onSubmit(): void {
    if (this.profileForm.invalid) {
      return;
    }

    this.updateLoading = true;
    this.error = '';
    this.success = '';

    const { username, providerName } = this.profileForm.value;

    const updateData: Partial<User> = {
      username
    };

    if (this.user?.role === 'provider') {
      updateData.providerName = providerName;
    }

    this.authService.updateProfile(updateData).subscribe({
      next: (updatedUser) => {
        this.updateLoading = false;
        this.success = 'Profile updated successfully!';
        this.user = updatedUser;
      },
      error: (err) => {
        this.updateLoading = false;
        this.error = err.message || 'Failed to update profile. Please try again.';
      }
    });
  }

  onLogout(): void {
    this.authService.logout();
  }
}
