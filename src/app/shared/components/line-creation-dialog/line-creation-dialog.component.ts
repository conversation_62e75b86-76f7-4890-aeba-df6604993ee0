import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar } from '@angular/material/snack-bar';
import { OpticalLineService } from '../../../core/services/optical-line.service';
import { ConnectionPointService } from '../../../core/services/connection-point.service';
import { AuthService } from '../../../core/services/auth.service';
import { OpticalLine } from '../../../core/models/optical-line.model';
import { ConnectionPoint } from '../../../core/models/connection-point.model';
import { User } from '../../../core/models/user.model';
import { forkJoin } from 'rxjs';

export interface LineCreationDialogData {
  startPoint: { latitude: number; longitude: number };
  endPoint: { latitude: number; longitude: number };
}

@Component({
  selector: 'app-line-creation-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatProgressSpinnerModule
  ],
  template: `
    <h2 mat-dialog-title>Add New Optical Line</h2>
    <mat-dialog-content>
      <form [formGroup]="lineForm" class="line-form">
        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Name</mat-label>
            <input matInput formControlName="name" placeholder="Enter line name">
            <mat-error *ngIf="lineForm.get('name')?.hasError('required')">
              Name is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Start Point</mat-label>
            <mat-select formControlName="startPointId">
              <mat-option value="">Select start point</mat-option>
              <mat-option *ngFor="let point of availablePoints" [value]="point.id">
                {{ point.name }} ({{ point.type }})
              </mat-option>
            </mat-select>
            <mat-error *ngIf="lineForm.get('startPointId')?.hasError('required')">
              Start point is required
            </mat-error>
          </mat-form-field>
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>End Point</mat-label>
            <mat-select formControlName="endPointId">
              <mat-option value="">Select end point</mat-option>
              <mat-option *ngFor="let point of availablePoints" [value]="point.id">
                {{ point.name }} ({{ point.type }})
              </mat-option>
            </mat-select>
            <mat-error *ngIf="lineForm.get('endPointId')?.hasError('required')">
              End point is required
            </mat-error>
          </mat-form-field>
        </div>

        <div *ngIf="lineForm.hasError('samePoints')" class="validation-error">
          Start and end points must be different.
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Capacity</mat-label>
            <input matInput type="number" formControlName="capacity" placeholder="Enter capacity">
            <mat-error *ngIf="lineForm.get('capacity')?.hasError('required')">
              Capacity is required
            </mat-error>
            <mat-error *ngIf="lineForm.get('capacity')?.hasError('min')">
              Capacity must be greater than 0
            </mat-error>
          </mat-form-field>
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Used Capacity</mat-label>
            <input matInput type="number" formControlName="usedCapacity" placeholder="Enter used capacity">
            <mat-error *ngIf="lineForm.get('usedCapacity')?.hasError('min')">
              Used capacity cannot be negative
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Length (km)</mat-label>
            <input matInput type="number" formControlName="length" step="0.1" placeholder="Enter length">
            <mat-error *ngIf="lineForm.get('length')?.hasError('required')">
              Length is required
            </mat-error>
            <mat-error *ngIf="lineForm.get('length')?.hasError('min')">
              Length must be greater than 0
            </mat-error>
          </mat-form-field>
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Status</mat-label>
            <mat-select formControlName="status">
              <mat-option value="active">Active</mat-option>
              <mat-option value="planned">Planned</mat-option>
              <mat-option value="maintenance">Maintenance</mat-option>
              <mat-option value="inactive">Inactive</mat-option>
            </mat-select>
            <mat-error *ngIf="lineForm.get('status')?.hasError('required')">
              Status is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Installation Date</mat-label>
            <input matInput [matDatepicker]="picker" formControlName="installationDate">
            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
            <mat-error *ngIf="lineForm.get('installationDate')?.hasError('required')">
              Installation date is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="coordinates-info">
          <h4>Line Coordinates</h4>
          <p><strong>Start:</strong> {{ data.startPoint.latitude | number:'1.6-6' }}, {{ data.startPoint.longitude | number:'1.6-6' }}</p>
          <p><strong>End:</strong> {{ data.endPoint.latitude | number:'1.6-6' }}, {{ data.endPoint.longitude | number:'1.6-6' }}</p>
        </div>
      </form>
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      <button mat-button (click)="onCancel()" [disabled]="loading">Cancel</button>
      <button mat-raised-button color="primary" (click)="onSave()" [disabled]="!lineForm.valid || loading">
        <mat-spinner diameter="20" *ngIf="loading"></mat-spinner>
        <span *ngIf="!loading">Create Line</span>
        <span *ngIf="loading">Creating...</span>
      </button>
    </mat-dialog-actions>
  `,
  styles: `
    .line-form {
      min-width: 500px;
      max-width: 600px;
    }

    .form-row {
      margin-bottom: 16px;
      display: flex;
      gap: 16px;
    }

    .full-width {
      width: 100%;
    }

    .half-width {
      flex: 1;
    }

    .coordinates-info {
      background-color: #f5f5f5;
      padding: 16px;
      border-radius: 4px;
      margin-top: 16px;
    }

    .coordinates-info h4 {
      margin: 0 0 8px 0;
      color: #666;
    }

    .coordinates-info p {
      margin: 4px 0;
      font-family: monospace;
    }

    mat-dialog-content {
      max-height: 70vh;
      overflow-y: auto;
    }

    mat-spinner {
      margin-right: 8px;
    }

    .validation-error {
      color: #f44336;
      font-size: 12px;
      margin-top: -8px;
      margin-bottom: 16px;
    }
  `
})
export class LineCreationDialogComponent implements OnInit {
  lineForm: FormGroup;
  loading = false;
  loadingPoints = true;
  currentUser: User | null = null;
  availablePoints: ConnectionPoint[] = [];

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<LineCreationDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: LineCreationDialogData,
    private lineService: OpticalLineService,
    private pointService: ConnectionPointService,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) {
    this.lineForm = this.fb.group({
      name: ['', [Validators.required]],
      startPointId: ['', [Validators.required]],
      endPointId: ['', [Validators.required]],
      capacity: [1000, [Validators.required, Validators.min(1)]],
      usedCapacity: [0, [Validators.min(0)]],
      length: [0, [Validators.required, Validators.min(0.1)]],
      status: ['active', [Validators.required]],
      installationDate: [new Date(), [Validators.required]]
    }, { validators: this.differentPointsValidator });
  }

  ngOnInit(): void {
    this.currentUser = this.authService.getCurrentUser();
    this.loadAvailablePoints();
    this.calculateDistance();
  }

  private loadAvailablePoints(): void {
    this.loadingPoints = true;
    this.pointService.getPoints().subscribe({
      next: (points) => {
        this.availablePoints = points;
        this.loadingPoints = false;
      },
      error: (error) => {
        console.error('Error loading points:', error);
        this.loadingPoints = false;
      }
    });
  }

  private calculateDistance(): void {
    // Calculate approximate distance using Haversine formula
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRad(this.data.endPoint.latitude - this.data.startPoint.latitude);
    const dLon = this.toRad(this.data.endPoint.longitude - this.data.startPoint.longitude);
    const lat1 = this.toRad(this.data.startPoint.latitude);
    const lat2 = this.toRad(this.data.endPoint.latitude);

    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.sin(dLon/2) * Math.sin(dLon/2) * Math.cos(lat1) * Math.cos(lat2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c;

    this.lineForm.patchValue({ length: Math.round(distance * 10) / 10 });
  }

  private toRad(value: number): number {
    return value * Math.PI / 180;
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onSave(): void {
    if (this.lineForm.valid && !this.loading) {
      this.loading = true;
      
      const formValue = this.lineForm.value;
      
      // Create the line data according to the API specification
      const lineData: Omit<OpticalLine, 'id'> = {
        name: formValue.name,
        providerId: this.currentUser?.id || '',
        providerName: this.currentUser?.providerName || this.currentUser?.username || '',
        startPointId: formValue.startPointId,
        endPointId: formValue.endPointId,
        capacity: formValue.capacity,
        usedCapacity: formValue.usedCapacity,
        length: formValue.length,
        status: formValue.status,
        installationDate: formValue.installationDate,
        lastModified: new Date(),
        geometry: {
          type: 'LineString',
          coordinates: [
            [this.data.startPoint.longitude, this.data.startPoint.latitude],
            [this.data.endPoint.longitude, this.data.endPoint.latitude]
          ]
        },
        properties: {}
      };

      this.lineService.createLine(lineData).subscribe({
        next: (createdLine) => {
          this.loading = false;
          this.snackBar.open('Optical line created successfully!', 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.dialogRef.close(createdLine);
        },
        error: (error) => {
          this.loading = false;
          console.error('Error creating line:', error);

          let errorMessage = 'Failed to create optical line. Please try again.';
          if (error.status === 401) {
            errorMessage = 'You are not authorized to create optical lines.';
          } else if (error.status === 400) {
            errorMessage = 'Invalid data provided. Please check your inputs.';
          } else if (error.error?.message) {
            errorMessage = error.error.message;
          }

          this.snackBar.open(errorMessage, 'Close', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }

  /**
   * Custom validator to ensure start and end points are different
   */
  private differentPointsValidator(control: any) {
    const startPointId = control.get('startPointId')?.value;
    const endPointId = control.get('endPointId')?.value;

    if (startPointId && endPointId && startPointId === endPointId) {
      return { samePoints: true };
    }

    return null;
  }
}
