import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { <PERSON><PERSON>uilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ConnectionPointService } from '../../../core/services/connection-point.service';
import { AuthService } from '../../../core/services/auth.service';
import { ConnectionPoint } from '../../../core/models/connection-point.model';
import { User } from '../../../core/models/user.model';

export interface PointCreationDialogData {
  latitude: number;
  longitude: number;
}

@Component({
  selector: 'app-point-creation-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatProgressSpinnerModule
  ],
  template: `
    <h2 mat-dialog-title>Add New Connection Point</h2>
    <mat-dialog-content>
      <form [formGroup]="pointForm" class="point-form">
        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Name</mat-label>
            <input matInput formControlName="name" placeholder="Enter point name">
            <mat-error *ngIf="pointForm.get('name')?.hasError('required')">
              Name is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Type</mat-label>
            <mat-select formControlName="type">
              <mat-option value="junction">Junction</mat-option>
              <mat-option value="endpoint">Endpoint</mat-option>
              <mat-option value="distribution">Distribution</mat-option>
            </mat-select>
            <mat-error *ngIf="pointForm.get('type')?.hasError('required')">
              Type is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Capacity</mat-label>
            <input matInput type="number" formControlName="capacity" placeholder="Enter capacity">
            <mat-error *ngIf="pointForm.get('capacity')?.hasError('required')">
              Capacity is required
            </mat-error>
            <mat-error *ngIf="pointForm.get('capacity')?.hasError('min')">
              Capacity must be greater than 0
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Address</mat-label>
            <input matInput formControlName="address" placeholder="Enter address">
            <mat-error *ngIf="pointForm.get('address')?.hasError('required')">
              Address is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Status</mat-label>
            <mat-select formControlName="status">
              <mat-option value="active">Active</mat-option>
              <mat-option value="planned">Planned</mat-option>
              <mat-option value="maintenance">Maintenance</mat-option>
              <mat-option value="inactive">Inactive</mat-option>
            </mat-select>
            <mat-error *ngIf="pointForm.get('status')?.hasError('required')">
              Status is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Installation Date</mat-label>
            <input matInput [matDatepicker]="picker" formControlName="installationDate">
            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
            <mat-error *ngIf="pointForm.get('installationDate')?.hasError('required')">
              Installation date is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row coordinates-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Latitude</mat-label>
            <input matInput type="number" formControlName="latitude" step="0.000001" readonly>
          </mat-form-field>
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Longitude</mat-label>
            <input matInput type="number" formControlName="longitude" step="0.000001" readonly>
          </mat-form-field>
        </div>
      </form>
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      <button mat-button (click)="onCancel()" [disabled]="loading">Cancel</button>
      <button mat-raised-button color="primary" (click)="onSave()" [disabled]="!pointForm.valid || loading">
        <mat-spinner diameter="20" *ngIf="loading"></mat-spinner>
        <span *ngIf="!loading">Create Point</span>
        <span *ngIf="loading">Creating...</span>
      </button>
    </mat-dialog-actions>
  `,
  styles: `
    .point-form {
      min-width: 400px;
      max-width: 500px;
    }

    .form-row {
      margin-bottom: 16px;
    }

    .full-width {
      width: 100%;
    }

    .coordinates-row {
      display: flex;
      gap: 16px;
    }

    .half-width {
      flex: 1;
    }

    mat-dialog-content {
      max-height: 70vh;
      overflow-y: auto;
    }

    mat-spinner {
      margin-right: 8px;
    }
  `
})
export class PointCreationDialogComponent implements OnInit {
  pointForm: FormGroup;
  loading = false;
  currentUser: User | null = null;

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<PointCreationDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: PointCreationDialogData,
    private pointService: ConnectionPointService,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) {
    this.pointForm = this.fb.group({
      name: ['', [Validators.required]],
      type: ['junction', [Validators.required]],
      capacity: [100, [Validators.required, Validators.min(1)]],
      address: ['', [Validators.required]],
      status: ['active', [Validators.required]],
      installationDate: [new Date(), [Validators.required]],
      latitude: [{ value: data.latitude, disabled: true }, [Validators.required]],
      longitude: [{ value: data.longitude, disabled: true }, [Validators.required]]
    });
  }

  ngOnInit(): void {
    this.currentUser = this.authService.getCurrentUser();
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onSave(): void {
    if (this.pointForm.valid && !this.loading) {
      this.loading = true;
      
      const formValue = this.pointForm.value;
      
      // Create the point data according to the API specification
      const pointData: Omit<ConnectionPoint, 'id'> = {
        name: formValue.name,
        providerId: this.currentUser?.id || '',
        providerName: this.currentUser?.providerName || this.currentUser?.username || '',
        type: formValue.type,
        capacity: formValue.capacity,
        address: formValue.address,
        status: formValue.status,
        installationDate: formValue.installationDate,
        lastModified: new Date(),
        latitude: this.data.latitude,
        longitude: this.data.longitude,
        location: {
          type: 'Point',
          coordinates: [this.data.longitude, this.data.latitude]
        },
        connectedLines: [],
        properties: {}
      };

      this.pointService.createPoint(pointData).subscribe({
        next: (createdPoint) => {
          this.loading = false;
          this.snackBar.open('Connection point created successfully!', 'Close', {
            duration: 3000,
            panelClass: ['success-snackbar']
          });
          this.dialogRef.close(createdPoint);
        },
        error: (error) => {
          this.loading = false;
          console.error('Error creating point:', error);

          let errorMessage = 'Failed to create connection point. Please try again.';
          if (error.status === 401) {
            errorMessage = 'You are not authorized to create connection points.';
          } else if (error.status === 400) {
            errorMessage = 'Invalid data provided. Please check your inputs.';
          } else if (error.error?.message) {
            errorMessage = error.error.message;
          }

          this.snackBar.open(errorMessage, 'Close', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      });
    }
  }
}
